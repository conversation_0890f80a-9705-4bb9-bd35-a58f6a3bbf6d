namespace Geometry.Core.Tests.Helper.Builders;

public static class PolygonBuilder
{
    public static List<PointD> CreateSquarePolygon(double x, double y, double size)
    {
        return new List<PointD>
        {
            new(x, y),
            new(x + size, y),
            new(x + size, y + size),
            new(x, y + size)
        };
    }

    public static List<PointD> CreateTrianglePolygon()
    {
        return new List<PointD>
        {
            new(0, 0),
            new(10, 0),
            new(5, 10)
        };
    }

    public static List<PointD> CreateLShapedPolygon()
    {
        return new List<PointD>
        {
            new(0, 0),
            new(3, 0),
            new(3, 2),
            new(5, 2),
            new(5, 5),
            new(0, 5)
        };
    }

    public static List<PointD> CreateCircularPolygon(PointD center, double radius, int vertexCount)
    {
        var polygon = new List<PointD>();
        for (int i = 0; i < vertexCount; i++)
        {
            var angle = 2 * Math.PI * i / vertexCount;
            var x = center.X + radius * Math.Cos(angle);
            var y = center.Y + radius * Math.Sin(angle);
            polygon.Add(new PointD(x, y));
        }
        return polygon;
    }

    public static List<PointD> CreateSectionPolygon()
    {
        var points = new List<PointD>()
        {
            new (61.702762182453, 36.121937263757),
            new (61.69818087093, 36.121066941828),
            new (61.060893268092, 36),
            new (60.781382535235, 35.947999999858),
            new (57.360126516316, 35),
            new (55.30051568558, 33.960501244292),
            new (54.247355651576, 33.960501244292),
            new (45.0407110292, 30.754631045274),
            new (44.985722118057, 30.739181136712),
            new (42.098292244482, 30),
            new (13.493153597345, 30),
            new (1.525065883296, 31),
            new (0, 31.19995883666),
            new (0, 26.970881381072),
            new (0, 17.075503322296),
            new (0, 0),
            new (140.070962705417, 0),
            new (140.069026004528, 19.79999981131),
            new (140.069006441893, 19.999999808652),
            new (140.068824081951, 21.864369693835),
            new (140.068664426915, 23.496614354369),
            new (140.068492124556, 25.258159829304),
            new (134.089562112233, 24.963203632273),
            new (133.38049212459, 25.373814951629),
            new (132.489492124529, 25.339374653995),
            new (130.943492124556, 24.515368012711),
            new (120.925283645388, 24.443492507723),
            new (118.678492124542, 24.427372931503),
            new (105.303492124542, 30.12056909129),
            new (99.114026504802, 30.062272773124),
            new (91.250483126612, 33.338081254624),
            new (84.619650742156, 34.102236862294),
            new (73.298945956864, 39.569657884538),
            new (68.93917784025, 39.53521845676),
            new (62.632945237216, 36.625861485489),
            new (61.702762182453, 36.121937263757)
        };

        return points;
    }
}
