using Application.Apis.Clients.Request;
using Application.Apis.Clients.Request.InspectionSheet;
using Application.Apis.Clients.Request.Instrument.GetAutomatedReadingsData;
using Application.Apis.Clients.Request.Notification;
using Application.Apis.Clients.Request.Occurrence;
using Application.Apis.Clients.Request.Simulation.List;
using Application.Apis.Clients.Request.Simulation.Patch;
using Application.Apis.Clients.Response;
using Application.Apis.Clients.Response.Clients;
using Application.Apis.Clients.Response.InspectionSheet;
using Application.Apis.Clients.Response.Instrument;
using Application.Apis.Clients.Response.Instrument.GetAutomatedReadingsData;
using Application.Apis.Clients.Response.Occurrence;
using Application.Apis.Clients.Response.OccurrenceListReport;
using Application.Apis.Clients.Response.Package.GetById;
using Application.Apis.Clients.Response.Reading;
using Application.Apis.Clients.Response.Report;
using Application.Apis.Clients.Response.Section.GetById;
using Application.Apis.Clients.Response.Simulation.GetById;
using Application.Apis.Clients.Response.Simulation.List;
using Application.Apis.Clients.Response.StabilityAnalysis.GetByIdWithSafetyFactorsMetrics;
using Application.Apis.Clients.Response.StaticMaterial.GetBySearchId;
using Application.Apis.Clients.Response.Structure.GetById;
using Application.Apis.Clients.Response.Structure.GetStabilityAnalysisInfo;
using Application.Apis.Clients.Response.User;
using Application.Apis.Keycloak;
using Application.Apis.Users.Request;
using Application.Apis.Users.Response;
using Refit;

namespace Application.Apis.Clients
{
    public class ClientsApiService : IClientsApiService
    {
        private readonly IKeycloakApiService _keycloakApiService;
        private readonly IClientsApi _clientsApi;

        public ClientsApiService(
            IKeycloakApiService keycloakApiService,
            IClientsApi clientsApi)
        {
            _keycloakApiService = keycloakApiService;
            _clientsApi = clientsApi;
        }

        public async Task<GetClientLogoResponse> GetClientLogoAsync(Guid id)
        {
            var token = await _keycloakApiService.GetApiToken();

            if (!token.IsSuccessStatusCode)
            {
                return null;
            }

            var response = await _clientsApi
                .GetClientLogo(id, token.Content.AccessToken);

            return response.IsSuccessStatusCode
                ? response.Content
                : null;
        }

        public async Task<List<GetBeachLengthStatsResponse>>
            GetBeachLengthStats(GetBeachLengthStatsRequest request)
        {
            var token = await _keycloakApiService.GetApiToken();

            if (!token.IsSuccessStatusCode)
            {
                return null;
            }

            var response = await _clientsApi
                .GetBeachLengthStats(request, token.Content.AccessToken);

            return response.IsSuccessStatusCode
                ? response.Content
                : null;
        }

        public async Task<List<GetReadingStatsResponse>> GetReadingStats(
            GetReadingStatsRequest request)
        {
            var token = await _keycloakApiService.GetApiToken();

            if (!token.IsSuccessStatusCode)
            {
                return null;
            }

            var response = await _clientsApi
                .GetReadingStats(request, token.Content.AccessToken);

            return response.IsSuccessStatusCode
                ? response.Content
                : null;
        }

        public async Task<Guid> DeletePackage(
            Guid packageId,
            Guid sectionId)
        {
            var token = await _keycloakApiService.GetApiToken();

            if (!token.IsSuccessStatusCode)
            {
                return Guid.Empty;
            }

            var response = await _clientsApi
                .DeletePackage(packageId, sectionId, token.Content.AccessToken);

            return response.IsSuccessStatusCode
                ? response.Content
                : Guid.Empty;
        }

        public async Task<bool> UpdateSimulationStatus(
            UpdateSimulationStatusRequest request)
        {
            var token = await _keycloakApiService.GetApiToken();

            if (!token.IsSuccessStatusCode)
            {
                throw token.Error;
            }

            var response = await _clientsApi
                .UpdateSimulationStatus(request.Id, request,
                    token.Content.AccessToken);

            return response.IsSuccessStatusCode;
        }

        public async Task<bool> UpdatePackageStatus(
            UpdatePackageStatusRequest request)
        {
            var token = await _keycloakApiService.GetApiToken();

            if (!token.IsSuccessStatusCode)
            {
                throw token.Error;
            }

            var response = await _clientsApi
                .UpdatePackageStatus(request.Id, request,
                    token.Content.AccessToken);

            return response.IsSuccessStatusCode;
        }

        public async Task<Guid> CreateStabilityAnalysis(
            AddStabilityAnalysisRequest request)
        {
            var token = await _keycloakApiService.GetApiToken();

            if (!token.IsSuccessStatusCode)
            {
                return Guid.Empty;
            }

            var response = await _clientsApi
                .CreateStabilityAnalysis(request, token.Content.AccessToken);

            return response.IsSuccessStatusCode
                ? response.Content
                : Guid.Empty;
        }

        public async Task<GetSimulationByIdResponse> GetSimulationById(Guid id)
        {
            var token = await _keycloakApiService.GetApiToken();

            if (!token.IsSuccessStatusCode)
            {
                return null;
            }

            var response = await _clientsApi
                .GetSimulationById(id, token.Content.AccessToken);

            return response.IsSuccessStatusCode
                ? response.Content
                : null;
        }

        public async Task<List<ListUserResponse>> GetUsersAsync(
            ListUserRequest request)
        {
            var token = await _keycloakApiService.GetApiToken();

            if (!token.IsSuccessStatusCode)
            {
                return null;
            }

            var response = await _clientsApi
                .GetUsers(request, token.Content.AccessToken);

            return response.IsSuccessStatusCode
                ? response.Content
                : null;
        }

        public async Task<decimal?> GetTotalRainfall(
            Guid instrumentId,
            DateTime readingValueDate)
        {
            var token = await _keycloakApiService.GetApiToken();

            if (!token.IsSuccessStatusCode)
            {
                return null;
            }

            var response = await _clientsApi
                .GetTotalRainfall(instrumentId, readingValueDate,
                    token.Content.AccessToken);

            return response.IsSuccessStatusCode
                ? response.Content
                : null;
        }

        public async Task<bool> AddSecurityLevelAlert(
            Guid id,
            AddInstrumentSecurityLevelAlertsRequest request)
        {
            var token = await _keycloakApiService.GetApiToken();

            if (!token.IsSuccessStatusCode)
            {
                throw token.Error;
            }

            var response = await _clientsApi
                .AddSecurityLevelAlert(id, request, token.Content.AccessToken);

            return response.IsSuccessStatusCode
                ? true
                : throw response.Error;
        }

        public async Task<List<GetStaticMaterialBySearchIdResponse>>
            GetStaticMaterialsBySearchIds(
                List<int> searchIds,
                DateTime? reviewDate)
        {
            var token = await _keycloakApiService.GetApiToken();

            if (!token.IsSuccessStatusCode)
            {
                return null;
            }

            var response = await _clientsApi
                .GetStaticMaterialsBySearchIds(reviewDate, searchIds,
                    token.Content.AccessToken);

            return response.IsSuccessStatusCode
                ? response.Content
                : null;
        }

        public async Task<GetStructureStabilityAnalysisInfoResponse>
            GetStructureStabilityAnalysisInfo(Guid id)
        {
            var token = await _keycloakApiService.GetApiToken();

            if (!token.IsSuccessStatusCode)
            {
                return null;
            }

            var response = await _clientsApi
                .GetStructureStabilityAnalysisInfo(id,
                    token.Content.AccessToken);

            return response.IsSuccessStatusCode
                ? response.Content
                : null;
        }

        public async Task<GetStructureByIdResponse> GetStructureById(
            Guid id)
        {
            var token = await _keycloakApiService.GetApiToken();

            if (!token.IsSuccessStatusCode)
            {
                return null;
            }

            var response = await _clientsApi
                .GetStructureById(id, token.Content.AccessToken);

            return response.IsSuccessStatusCode
                ? response.Content
                : null;
        }

        public async Task<HttpResponseMessage> AddSliToSectionReview(
            AddSliFileRequest request)
        {
            var token = await _keycloakApiService.GetApiToken();

            if (!token.IsSuccessStatusCode)
            {
                return null;
            }

            return await _clientsApi
                .AddSliToSectionReview(request.SectionId, request.ReviewId,
                    request, token.Content.AccessToken);
        }

        public async Task<List<SearchOccurrenceUnpaginatedResponse>> SearchOccurrenceUnpaginatedAsync(
            List<Guid> structures, 
            SearchOccurrenceUnpaginatedRequest request)
        {
            var token = await _keycloakApiService.GetApiToken();

            if (!token.IsSuccessStatusCode)
            {
                return null;
            }

            var response = await _clientsApi
                .SearchOccurrenceUnpaginated(structures, request, token.Content.AccessToken);

            return response.IsSuccessStatusCode
                ? response.Content
                : null;
        }

        public async Task<GetOccurrenceListReportByIdResponse> GetOccurrenceListReportAsync(Guid id)
        {
            var token = await _keycloakApiService.GetApiToken();

            if (!token.IsSuccessStatusCode)
            {
                return null;
            }

            var response = await _clientsApi
                .GetOccurrenceListReportById(id, token.Content.AccessToken);    

            return response.IsSuccessStatusCode
                ? response.Content
                : null;
        }

        public async Task<GetSectionByIdResponse> GetSectionById(
            Guid id)
        {
            var token = await _keycloakApiService.GetApiToken();

            if (!token.IsSuccessStatusCode)
            {
                return null;
            }

            var response = await _clientsApi
                .GetSectionWithSliById(id, token.Content.AccessToken);

            return response.IsSuccessStatusCode
                ? response.Content
                : null;
        }

        public async Task<GetPackageByIdResponse> GetPackageById(
            Guid id)
        {
            var token = await _keycloakApiService.GetApiToken();

            if (!token.IsSuccessStatusCode)
            {
                return null;
            }

            var response = await _clientsApi
                .GetPackageById(id, token.Content.AccessToken);

            return response.IsSuccessStatusCode
                ? response.Content
                : null;
        }

        public async Task<GetInstrumentByIdResponse> GetInstrumentById(
            Guid id)
        {
            var token = await _keycloakApiService.GetApiToken();

            if (!token.IsSuccessStatusCode)
            {
                return null;
            }

            var response = await _clientsApi
                .GetInstrumentById(id, token.Content.AccessToken);

            return response.IsSuccessStatusCode
                ? response.Content
                : null;
        }

        public async Task<IEnumerable<GetInstrumentByIdResponse>> GetInstrumentsByIds(
            IEnumerable<Guid> ids)
        {
            var token = await _keycloakApiService.GetApiToken();

            if (!token.IsSuccessStatusCode)
            {
                return null;
            }

            var response = await _clientsApi
                .GetInstrumentsByIds(ids, token.Content.AccessToken);

            return response.IsSuccessStatusCode
                ? response.Content.Instruments
                : Enumerable.Empty<GetInstrumentByIdResponse>();
        }

        public async Task<GetReadingByIdResponse> GetReadingById(
            Guid id)
        {
            var token = await _keycloakApiService.GetApiToken();

            if (!token.IsSuccessStatusCode)
            {
                return null;
            }

            var response = await _clientsApi
                .GetReadingById(id, token.Content.AccessToken);

            return response.IsSuccessStatusCode
                ? response.Content
                : null;
        }

        public async Task<List<GetExpiringClientsResponse>> GetExpiringClients()
        {
            var token = await _keycloakApiService.GetApiToken();

            if (!token.IsSuccessStatusCode)
            {
                return null;
            }

            var response = await _clientsApi
                .GetExpiringClients(token.Content.AccessToken);

            return response.IsSuccessStatusCode
                ? response.Content
                : null;
        }

        public async Task<IEnumerable<GetUserEmailResponse>> GetUsersEmails(
            GetUserEmailRequest request)
        {
            var token = await _keycloakApiService.GetApiToken();

            if (!token.IsSuccessStatusCode)
            {
                return null;
            }

            var response = await _clientsApi
                .GetUsersEmails(
                    request,
                    token.Content.AccessToken);

            return response.IsSuccessStatusCode
                ? response.Content
                : null;
        }

        public async Task<HttpResponseMessage> DeleteNotifications()
        {
            var token = await _keycloakApiService.GetApiToken();

            if (!token.IsSuccessStatusCode)
            {
                return null;
            }

            var response = await _clientsApi
                .DeleteNotifications(token.Content.AccessToken);

            return response;
        }

        public async Task<GetStaticMaterialBySearchIdResponse>
            GetStaticMaterialById(
                Guid id)
        {
            var token = await _keycloakApiService.GetApiToken();

            if (!token.IsSuccessStatusCode)
            {
                return null;
            }

            var response = await _clientsApi
                .GetStaticMaterialById(id, token.Content.AccessToken);

            return response.IsSuccessStatusCode
                ? response.Content
                : null;
        }

        public async Task<HttpResponseMessage> AddNotification(
            AddNotificationRequest request)
        {
            var token = await _keycloakApiService.GetApiToken();

            if (!token.IsSuccessStatusCode)
            {
                return null;
            }

            var response = await _clientsApi
                .AddNotification(
                    request,
                    token.Content.AccessToken
                );

            return response;
        }

        public async Task<IEnumerable<GetPendingReportResponse>>
            GetPendingReports(
                DateTime startDate,
                DateTime endDate)
        {
            var token = await _keycloakApiService.GetApiToken();

            if (!token.IsSuccessStatusCode)
            {
                return null;
            }

            var response = await _clientsApi.GetPendingReports(
                startDate,
                endDate,
                token.Content.AccessToken);

            return response.IsSuccessStatusCode
                ? response.Content
                : null;
        }

        public async Task<bool> CheckIfNotificationExists(ExistsNotificationRequest request)
        {
            var token = await _keycloakApiService.GetApiToken();

            if (!token.IsSuccessStatusCode)
            {
                return false;
            }

            var response = await _clientsApi.CheckIfNotificationExists(
                request,
                token.Content.AccessToken);

            return response.IsSuccessStatusCode ? response.Content : false;
        }

        public async Task<bool> UpdateReportEmissionDates(
            Guid id,
            DateTime nextEmissionDate,
            DateTime lastEmissionDate)
        {
            var token = await _keycloakApiService.GetApiToken();

            if (!token.IsSuccessStatusCode)
            {
                return false;
            }

            var requestBody = new UpdateReportEmissionDatesRequest
            {
                NextEmissionDate = nextEmissionDate,
                LastEmissionDate = lastEmissionDate
            };

            var response = await _clientsApi.UpdateReportEmissionDates(
                id,
                requestBody,
                token.Content.AccessToken);

            return response.IsSuccessStatusCode;
        }

        public async Task<T> GetReportData<T>(
            Guid reportId)
        {
            var token = await _keycloakApiService.GetApiToken();

            if (!token.IsSuccessStatusCode)
            {
                return default(T);
            }

            var response =
                await _clientsApi.GetReportData<T>(reportId,
                    token.Content.AccessToken);

            return response.IsSuccessStatusCode
                ? response.Content
                : default(T);
        }

        public async Task<GetUserByIdResponse> GetUserById(
            Guid id)
        {
            var token = await _keycloakApiService.GetApiToken();

            if (!token.IsSuccessStatusCode)
            {
                return null;
            }

            var response = await _clientsApi
                .GetUserById(id, token.Content.AccessToken);

            return response.IsSuccessStatusCode
                ? response.Content
                : null;
        }

        public async Task<IEnumerable<Guid>> GetRelatedReadingValues(Guid id)
        {
            var token = await _keycloakApiService.GetApiToken();

            if (!token.IsSuccessStatusCode)
            {
                return null;
            }

            var response = await _clientsApi
                .GetRelatedReadingValues(id, token.Content.AccessToken);

            return response.IsSuccessStatusCode
                ? response.Content
                : null;
        }

        public async Task<IEnumerable<Guid>> AddPackage(
            AddPackageRequest request)
        {
            var token = await _keycloakApiService.GetApiToken();

            if (!token.IsSuccessStatusCode)
            {
                throw token.Error;
            }

            var response = await _clientsApi
                .AddPackage(request.ReadingValues, token.Content.AccessToken);

            return response.IsSuccessStatusCode
                ? response.Content
                : throw response.Error;
        }

        public async Task<Guid> CalculatePackageStability(
            CalculatePackageStabilityRequest request)
        {
            var token = await _keycloakApiService.GetApiToken();

            if (!token.IsSuccessStatusCode)
            {
                throw token.Error;
            }

            var response = await _clientsApi
                .CalculatePackageStability(request.PackageId, request,
                    token.Content.AccessToken);

            return response.IsSuccessStatusCode
                ? response.Content
                : throw response.Error;
        }

        public async
            Task<GetStabilityAnalysisByIdWithSafetyFactorsMetricsResponse>
            GetStabilityAnalysisWithSafetyFactorsMetricsAsync(
                Guid id)
        {
            var token = await _keycloakApiService.GetApiToken();
            if (!token.IsSuccessStatusCode)
            {
                return null;
            }

            var response = await _clientsApi
                .GetStabilityAnalysisWithSafetyFactorsMetricsAsync(
                    id,
                    token.Content.AccessToken);

            return response.IsSuccessStatusCode
                ? response.Content
                : null;
        }

        public async Task<GetAdjacentReadingValuesResponse>
            GetInstrumentAdjacentReadingValues(
                Guid id,
                Guid? measurementId,
                DateTime date)
        {
            var token = await _keycloakApiService.GetApiToken();
            if (!token.IsSuccessStatusCode)
            {
                return null;
            }

            var response = await _clientsApi
                .GetAdjacentReadingValues(id, measurementId, date,
                    token.Content.AccessToken);

            return response.IsSuccessStatusCode
                ? response.Content
                : null;
        }

        public async Task<HttpResponseMessage> PatchSimulation(
            PatchSimulationRequest request)
        {
            var token = await _keycloakApiService.GetApiToken();

            if (!token.IsSuccessStatusCode)
            {
                return null;
            }

            var response = await _clientsApi.PatchSimulation(request.Id,
                request, token.Content.AccessToken);

            return response;
        }

        public async Task<IEnumerable<
                GetClientAutomatedReadingsConfigurationsResponse>>
            GetEnabledClientAutomatedReadingsConfigurationsAsync()
        {
            var token = await _keycloakApiService.GetApiToken();
            if (!token.IsSuccessStatusCode)
            {
                return null;
            }

            var response = await _clientsApi
                .GetClientAutomatedReadingsConfigurationsAsync(token.Content
                    .AccessToken);

            return response.IsSuccessStatusCode
                ? response.Content
                : null;
        }

        public async Task<ApiResponse<IEnumerable<Guid>>> AddReadingsAsync(
            IEnumerable<AddReadingRequest> readings)
        {
            var token = await _keycloakApiService.GetApiToken();
            if (!token.IsSuccessStatusCode)
            {
                throw token.Error;
            }

            var response = await _clientsApi.AddReadingsAsync(
                readings,
                token.Content.AccessToken);

            return response;
        }

        public async Task<List<ListSimulationResponse>> ListSimulations(
            ListSimulationRequest request)
        {
            var token = await _keycloakApiService.GetApiToken();

            if (!token.IsSuccessStatusCode)
            {
                return null;
            }

            var response = await _clientsApi
                .ListSimulations(request, token.Content.AccessToken);

            return response.IsSuccessStatusCode
                ? response.Content
                : null;
        }

        public async Task<HttpResponseMessage> DeleteSimulation(Guid id)
        {
            var token = await _keycloakApiService.GetApiToken();

            if (!token.IsSuccessStatusCode)
            {
                return null;
            }

            return await _clientsApi.DeleteSimulation(id,
                token.Content.AccessToken);
        }

        public async Task<List<GetAutomatedReadingsDataResponse>>
            GetAutomatedReadingsDataAsync(
                GetAutomatedReadingsDataRequest request)
        {
            var token = await _keycloakApiService.GetApiToken();
            if (!token.IsSuccessStatusCode)
            {
                throw token.Error;
            }

            var response = await _clientsApi.GetAutomatedReadingsDataAsync(
                request,
                token.Content.AccessToken);

            return response.IsSuccessStatusCode
                ? response.Content
                : throw new HttpRequestException(
                    message:
                    $"Error: {response.Error?.Content ?? response.Error?.Message}",
                    response.Error);
        }

        public async Task<GetInspectionSheetByIdResponse> GetInspectionSheetAsync(
            Guid id)
        {
            var token = await _keycloakApiService.GetApiToken();
            if (!token.IsSuccessStatusCode)
            {
                throw token.Error;
            }

            var response = await _clientsApi.GetInspectionSheet(
                id,
                token.Content.AccessToken);
            
            return response.IsSuccessStatusCode
                ? response.Content
                : throw response.Error;
        }

        public async Task UpdateInspectionSheetAsync(
            UpdateInspectionSheetRequest request)
        {
            var token = await _keycloakApiService.GetApiToken();
            if (!token.IsSuccessStatusCode)
            {
                throw token.Error;
            }
            
            var response = await _clientsApi.UpdateInspectionSheet(
                request.Id,
                request.UserCompletedTheInspection,
                request.TranscriptionHasFinished,
                request,
                token.Content.AccessToken);

            if (!response.IsSuccessStatusCode)
            {
                throw response.Error!;
            }
        }
    }
}