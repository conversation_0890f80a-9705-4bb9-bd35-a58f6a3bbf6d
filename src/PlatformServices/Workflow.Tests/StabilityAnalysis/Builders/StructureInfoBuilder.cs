using Application.Apis.Clients.Response.Section;
using Coordinate.Core.Classes;
using Coordinate.Core.Enums;
using Domain.Enums;
using Dxf.Core.Extensions;
using Geometry.Core;
using IxMilia.Dxf;
using IxMilia.Dxf.Entities;
using Slide.Core;
using System.Text;
using Workflow.StabilityAnalysis.Classes;

namespace Workflow.Tests.StabilityAnalysis.Builders
{
    public static class StructureInfoBuilder
    {
        private static StructureInfo CreateDefault(string dxfBase64, string sliBase64)
        {
            return new StructureInfo()
            {
                StaticMaterialReviewDate = null,
                StructureId = Guid.NewGuid(),
                StructureName = "SantaRita",
                Slide2Configuration = new()
                {
                    CircularParameters = new()
                    {
                        CalculationMethods = new()
                        {
                            (CalculationMethod)1
                        },
                        CircularSearchMethod = (CircularSearchMethod)1,
                        DivisionsAlongSlope = 30,
                        CirclesPerDivision = 12,
                        NumberOfIterations = 12,
                        DivisionsNextIteration = 50,
                        RadiusIncrement = 30,
                        NumberOfSurfaces = 50000
                    },
                    NonCircularParameters = null
                },
                SafetyFactorTarget = null,
                SeismicCoefficient = null,
                ShouldEvaluateDrainedCondition = true,
                ShouldEvaluatePseudoStaticCondition = false,
                ShouldEvaluateUndrainedCondition = false,
                Sections = new()
                {
                    new()
                    {
                        SectionId =  Guid.NewGuid(),
                        SectionName = "K-K",
                        IsSkew = false,
                        Coordinates = new()
                        {
                            Datum = (Datum)2,
                            UpstreamCoordinateSetting = new()
                            {
                                CoordinateFormat = (Coordinate.Core.Enums.Format)1,
                                CoordinateSystems = new()
                                {
                                    Utm = new Utm
                                    {
                                        ZoneNumber = 24,
                                        ZoneLetter = 'L',
                                        Easting = 424900.39766945917,
                                        Northing = 8431784.410635721
                                    },
                                    DecimalGeodetic = new DecimalGeodetic
                                    {
                                        Latitude = -14.184252,
                                        Longitude = -39.695969
                                    }
                                }
                            },
                            DownstreamCoordinateSetting = new()
                            {
                                CoordinateFormat = (Coordinate.Core.Enums.Format)1,
                                CoordinateSystems = new()
                                {
                                    Utm = new Utm
                                    {
                                        ZoneNumber = 24,
                                        ZoneLetter = 'L',
                                        Easting = 425291.4373917451,
                                        Northing = 8431868.74643495
                                    },
                                    DecimalGeodetic = new()
                                    {
                                        Latitude = -14.1835,
                                        Longitude = -39.692343
                                    }
                                }
                            },
                            MidpointCoordinateSetting = null
                        },
                        SectionReviewData = new SectionReviewData
                        {
                            SectionReview = new SectionReview
                            {
                                Id =  Guid.NewGuid(),
                                StartDate = DateTime.UtcNow,
                                StructureType = new()
                                {
                                    Id = Guid.NewGuid(),
                                    Name = "Teste 0003 - Salvando edição",
                                    Description = null,
                                    Active = false,
                                    Activities = new()
                                    {
                                        new()
                                        {
                                            Id = Guid.NewGuid(),
                                            Activity = Activity.SeparateInstruments,
                                            Index = 0
                                        }
                                    }
                                },
                                Drawing = null,
                                Sli = null,
                                ConstructionStages = new List<ConstructionStage>(),
                                DxfHasWaterline = false
                            },
                            ConstructionStage = null,
                            Dxf = new()
                            {
                                Name = "structure1.dxf",
                                Base64 = dxfBase64
                            },
                            Sli = new()
                            {
                                Name = "structure1.sli",
                                Base64 = sliBase64
                            }
                        },
                        Instruments = new()
                        {
                            new()
                            {
                                InstrumentId = Guid.Parse("4cee7fd1-92c9-4b30-a973-6c79a54dab79"),
                                MeasurementId = null,
                                InstrumentType = (InstrumentType)2,
                                LinimetricRulerPosition = null,
                                InstrumentDryType = (DryType)2,
                                ReadingQuota = 721.893m,
                                DryReading = false
                            },
                            new()
                            {
                                InstrumentId = Guid.Parse("6ba19301-718a-4c96-9d44-00b317567485"),
                                MeasurementId = null,
                                InstrumentType = (InstrumentType)1,
                                LinimetricRulerPosition = null,
                                InstrumentDryType = (DryType)2,
                                ReadingQuota = 750.5m,
                                DryReading = false
                            }
                        },
                        BeachLength = null,
                        UpstreamLinimetricRulerQuota = 835,
                        DownstreamLinimetricRulerQuota = 798,
                        MinimumDrainedDepth = 1,
                        MinimumUndrainedDepth = null,
                        MinimumPseudoStaticDepth = null
                    }
                }
            };
        }

        public static StructureInfo CreateScenario1()
        {
            var currentDirectory = Directory.GetCurrentDirectory();

            var dxfPath = Path.Combine(currentDirectory, "StabilityAnalysis", "Files", "structure1.dxf");
            var sliPath = Path.Combine(currentDirectory, "StabilityAnalysis", "Files", "structure1.sli");

            var dxf = System.IO.File.ReadAllText(dxfPath);
            var sli = System.IO.File.ReadAllText(sliPath);

            var dxfBase64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(dxf));
            var sliBase64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(sli));

            return CreateDefault(dxfBase64, sliBase64);
        }

        public static StructureInfo CreateScenario2()
        {
            var currentDirectory = Directory.GetCurrentDirectory();

            var dxfPath = Path.Combine(currentDirectory, "StabilityAnalysis", "Files", "structure1.dxf");
            var sliPath = Path.Combine(currentDirectory, "StabilityAnalysis", "Files", "structure1.sli");

            var dxf = System.IO.File.ReadAllText(dxfPath);
            var sli = System.IO.File.ReadAllText(sliPath);

            var dxfBase64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(dxf));
            var sliBase64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(sli));

            var structureInfo = CreateDefault(dxfBase64, sliBase64);

            structureInfo.Sections[0].UpstreamLinimetricRulerQuota = 798;

            return structureInfo;
        }

        public static StructureInfo CreateScenario3()
        {
            var currentDirectory = Directory.GetCurrentDirectory();

            var dxfPath = Path.Combine(currentDirectory, "StabilityAnalysis", "Files", "structure1.dxf");
            var sliPath = Path.Combine(currentDirectory, "StabilityAnalysis", "Files", "structure1.sli");

            var dxf = System.IO.File.ReadAllText(dxfPath);
            var sli = System.IO.File.ReadAllText(sliPath);

            var dxfBase64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(dxf));
            var sliBase64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(sli));

            var structureInfo = CreateDefault(dxfBase64, sliBase64);
            structureInfo.Sections[0].SectionReviewData.SectionReview.StructureType.Activities[0].Activity = Activity.CreateReservoir;
            structureInfo.Sections[0].UpstreamLinimetricRulerQuota = 500;

            return structureInfo;
        }

        public static StructureInfo CreateScenario4()
        {
            var currentDirectory = Directory.GetCurrentDirectory();

            var dxfPath = Path.Combine(currentDirectory, "StabilityAnalysis", "Files", "structure1.dxf");
            var sliPath = Path.Combine(currentDirectory, "StabilityAnalysis", "Files", "structure1.sli");

            var dxf = System.IO.File.ReadAllText(dxfPath);
            var sli = System.IO.File.ReadAllText(sliPath);

            var dxfBase64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(dxf));
            var sliBase64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(sli));

            var structureInfo = CreateDefault(dxfBase64, sliBase64);
            structureInfo.Sections[0].SectionReviewData.SectionReview.StructureType.Activities.Add(new()
            {
                Id = Guid.NewGuid(),
                Activity = Activity.CreateReservoir,
                Index = 1
            });
            structureInfo.Sections[0].Instruments[0].ReadingQuota = 717.893m;

            return structureInfo;
        }

        public static StructureInfo CreateScenario5()
        {
            var currentDirectory = Directory.GetCurrentDirectory();

            var dxfPath = Path.Combine(currentDirectory, "StabilityAnalysis", "Files", "structure1.dxf");
            var sliPath = Path.Combine(currentDirectory, "StabilityAnalysis", "Files", "structure1.sli");

            var dxf = System.IO.File.ReadAllText(dxfPath);
            var sli = System.IO.File.ReadAllText(sliPath);

            var dxfBase64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(dxf));
            var sliBase64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(sli));

            var structureInfo = CreateDefault(dxfBase64, sliBase64);
            structureInfo.Sections[0].SectionReviewData.SectionReview.StructureType.Activities.Clear();
            structureInfo.Sections[0].DownstreamLinimetricRulerQuota = 758;
            structureInfo.Sections[0].Instruments[0].ReadingQuota = 811;

            return structureInfo;
        }

        public static StructureInfo CreateScenario6()
        {
            var currentDirectory = Directory.GetCurrentDirectory(); 

            var dxfPath = Path.Combine(currentDirectory, "StabilityAnalysis", "Files", "structure1.dxf");
            var dxfText = System.IO.File.ReadAllText(dxfPath);
            var dxfBase64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(dxfText));

            var dxf = new DxfFile();
            dxf = dxf.LoadWithBase64(dxfBase64);

            var allPoints = dxf.GetAllPoints();

            var minPoint = new PointD(allPoints.Min(x => x.X), allPoints.Min(x => x.Y));
            var maxPoint = new PointD(allPoints.Max(x => x.X), allPoints.Max(x => x.Y));

            var layer = new DxfLayer("freática");
            dxf.Layers.Add(layer);

            var vertices = new List<DxfVertex>
            {
                new DxfVertex(new(minPoint.X, minPoint.Y, 0)),
                new DxfVertex(new(minPoint.X + 189, minPoint.Y + 50, 0)),
                new DxfVertex(new(minPoint.X + 100, minPoint.Y + 100, 0)),
                new DxfVertex(new(maxPoint.X, maxPoint.Y - 50, 0))
            };

            var polyline = new DxfPolyline(vertices)
            {
                Layer = layer.Name
            };

            dxf.Entities.Add(polyline);

            using var ms = new MemoryStream();
            dxf.Save(ms);
            ms.Position = 0;
            dxfBase64 = Convert.ToBase64String(ms.ToArray());

            var sliPath = Path.Combine(currentDirectory, "StabilityAnalysis", "Files", "structure1.sli");
            var sli = System.IO.File.ReadAllText(sliPath);
            var sliBase64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(sli));

            var structureInfo = CreateDefault(dxfBase64, sliBase64);
            structureInfo.Sections[0].SectionReviewData.SectionReview.DxfHasWaterline = true;
            structureInfo.Sections[0].Instruments.Clear();
            structureInfo.Sections[0].DownstreamLinimetricRulerQuota = null;
            structureInfo.Sections[0].UpstreamLinimetricRulerQuota = null;


            return structureInfo;
        }

        public static StructureInfo CreateScenario7()
        {
            var currentDirectory = Directory.GetCurrentDirectory();

            var dxfPath = Path.Combine(currentDirectory, "StabilityAnalysis", "Files", "structure1.dxf");
            var dxfText = System.IO.File.ReadAllText(dxfPath);
            var dxfBase64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(dxfText));

            var dxf = new DxfFile();
            dxf = dxf.LoadWithBase64(dxfBase64);

            var allPoints = dxf.GetAllPoints();

            var minPoint = new PointD(allPoints.Min(x => x.X), allPoints.Min(x => x.Y));
            var maxPoint = new PointD(allPoints.Max(x => x.X), allPoints.Max(x => x.Y));

            var layer = new DxfLayer("freática");
            dxf.Layers.Add(layer);

            var vertices = new List<DxfVertex>
            {
                new DxfVertex(new(minPoint.X, minPoint.Y, 0)),
                new DxfVertex(new(minPoint.X + 189, minPoint.Y + 50, 0)),
                new DxfVertex(new(minPoint.X + 100, minPoint.Y + 100, 0)),
                new DxfVertex(new(maxPoint.X, maxPoint.Y - 50, 0))
            };

            var polyline = new DxfPolyline(vertices)
            {
                Layer = layer.Name
            };

            dxf.Entities.Add(polyline);

            using var ms = new MemoryStream();
            dxf.Save(ms);
            ms.Position = 0;
            dxfBase64 = Convert.ToBase64String(ms.ToArray());

            var sliPath = Path.Combine(currentDirectory, "StabilityAnalysis", "Files", "structure1.sli");
            var sli = System.IO.File.ReadAllText(sliPath);
            var sliBase64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(sli));

            var structureInfo = CreateDefault(dxfBase64, sliBase64);
            structureInfo.Sections[0].SectionReviewData.SectionReview.DxfHasWaterline = true;
            structureInfo.Sections[0].DownstreamLinimetricRulerQuota = null;
            structureInfo.Sections[0].UpstreamLinimetricRulerQuota = null;


            return structureInfo;
        }

        public static StructureInfo CreateScenario8()
        {
            var currentDirectory = Directory.GetCurrentDirectory();

            var dxfPath = Path.Combine(currentDirectory, "StabilityAnalysis", "Files", "structure1.dxf");
            var dxfText = System.IO.File.ReadAllText(dxfPath);
            var dxfBase64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(dxfText));

            var dxf = new DxfFile();
            dxf = dxf.LoadWithBase64(dxfBase64);

            var allPoints = dxf.GetAllPoints();

            var minPoint = new PointD(allPoints.Min(x => x.X), allPoints.Min(x => x.Y));
            var maxPoint = new PointD(allPoints.Max(x => x.X), allPoints.Max(x => x.Y));

            var layer = new DxfLayer("freática");
            dxf.Layers.Add(layer);

            var vertices = new List<DxfVertex>
            {
                new DxfVertex(new(minPoint.X, minPoint.Y, 0)),
                new DxfVertex(new(minPoint.X + 189, minPoint.Y + 50, 0)),
                new DxfVertex(new(minPoint.X + 100, minPoint.Y + 100, 0)),
                new DxfVertex(new(maxPoint.X, maxPoint.Y - 50, 0))
            };

            var polyline = new DxfPolyline(vertices)
            {
                Layer = layer.Name
            };

            dxf.Entities.Add(polyline);

            using var ms = new MemoryStream();
            dxf.Save(ms);
            ms.Position = 0;
            dxfBase64 = Convert.ToBase64String(ms.ToArray());

            var sliPath = Path.Combine(currentDirectory, "StabilityAnalysis", "Files", "structure1.sli");
            var sli = System.IO.File.ReadAllText(sliPath);
            var sliBase64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(sli));

            var structureInfo = CreateDefault(dxfBase64, sliBase64);
            structureInfo.Sections[0].Instruments[0].ReadingQuota = 766.87m;
            structureInfo.Sections[0].SectionReviewData.SectionReview.DxfHasWaterline = true;
            structureInfo.Sections[0].DownstreamLinimetricRulerQuota = null;
            structureInfo.Sections[0].UpstreamLinimetricRulerQuota = null;


            return structureInfo;
        }

        public static StructureInfo CreateScenario9()
        {
            var currentDirectory = Directory.GetCurrentDirectory();

            var dxfPath = Path.Combine(currentDirectory, "StabilityAnalysis", "Files", "structure1.dxf");
            var dxfText = System.IO.File.ReadAllText(dxfPath);
            var dxfBase64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(dxfText));

            var dxf = new DxfFile();
            dxf = dxf.LoadWithBase64(dxfBase64);

            var allPoints = dxf.GetAllPoints();

            var minPoint = new PointD(allPoints.Min(x => x.X), allPoints.Min(x => x.Y));
            var maxPoint = new PointD(allPoints.Max(x => x.X), allPoints.Max(x => x.Y));

            var layer = new DxfLayer("freática");
            dxf.Layers.Add(layer);

            var vertices = new List<DxfVertex>
            {
                new DxfVertex(new(minPoint.X, minPoint.Y, 0)),
                new DxfVertex(new(minPoint.X + 189, minPoint.Y + 50, 0)),
                new DxfVertex(new(minPoint.X + 100, minPoint.Y + 100, 0)),
                new DxfVertex(new(maxPoint.X, maxPoint.Y - 50, 0))
            };

            var polyline = new DxfPolyline(vertices)
            {
                Layer = layer.Name
            };

            dxf.Entities.Add(polyline);

            using var ms = new MemoryStream();
            dxf.Save(ms);
            ms.Position = 0;
            dxfBase64 = Convert.ToBase64String(ms.ToArray());

            var sliPath = Path.Combine(currentDirectory, "StabilityAnalysis", "Files", "structure1.sli");
            var sli = System.IO.File.ReadAllText(sliPath);
            var sliBase64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(sli));

            var structureInfo = CreateDefault(dxfBase64, sliBase64);
            structureInfo.Sections[0].Instruments[0].ReadingQuota = 756.87m;
            structureInfo.Sections[0].Instruments[1].ReadingQuota = 766.79m;
            structureInfo.Sections[0].SectionReviewData.SectionReview.DxfHasWaterline = true;
            structureInfo.Sections[0].DownstreamLinimetricRulerQuota = 758;
            structureInfo.Sections[0].UpstreamLinimetricRulerQuota = 798;

            return structureInfo;
        }
            
        public static StructureInfo CreateScenario10()
        {
            var currentDirectory = Directory.GetCurrentDirectory();

            var dxfPath = Path.Combine(currentDirectory, "StabilityAnalysis", "Files", "structure1.dxf");
            var dxfText = System.IO.File.ReadAllText(dxfPath);
            var dxfBase64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(dxfText));

            var dxf = new DxfFile();
            dxf = dxf.LoadWithBase64(dxfBase64);

            var allPoints = dxf.GetAllPoints();

            var minPoint = new PointD(allPoints.Min(x => x.X), allPoints.Min(x => x.Y));
            var maxPoint = new PointD(allPoints.Max(x => x.X), allPoints.Max(x => x.Y));

            var layer = new DxfLayer("freática");
            dxf.Layers.Add(layer);

            var vertices = new List<DxfVertex>
            {
                new DxfVertex(new(minPoint.X, minPoint.Y, 0)),
                new DxfVertex(new(minPoint.X + 189, minPoint.Y + 50, 0)),
                new DxfVertex(new(minPoint.X + 100, minPoint.Y + 100, 0)),
                new DxfVertex(new(maxPoint.X, maxPoint.Y - 50, 0))
            };

            var polyline = new DxfPolyline(vertices)
            {
                Layer = layer.Name
            };

            dxf.Entities.Add(polyline);

            using var ms = new MemoryStream();
            dxf.Save(ms);
            ms.Position = 0;
            dxfBase64 = Convert.ToBase64String(ms.ToArray());

            var sliPath = Path.Combine(currentDirectory, "StabilityAnalysis", "Files", "structure1.sli");
            var sli = System.IO.File.ReadAllText(sliPath);
            var sliBase64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(sli));

            var structureInfo = CreateDefault(dxfBase64, sliBase64);
            structureInfo.Sections[0].Instruments[0].ReadingQuota = 756.87m;
            structureInfo.Sections[0].Instruments[1].ReadingQuota = 766.79m;
            structureInfo.Sections[0].SectionReviewData.SectionReview.DxfHasWaterline = true;
            structureInfo.Sections[0].DownstreamLinimetricRulerQuota = 800;
            structureInfo.Sections[0].UpstreamLinimetricRulerQuota = 831;
            structureInfo.Sections[0].SectionReviewData.SectionReview.StructureType.Activities.Add(new()
            {
                Id = Guid.NewGuid(),
                Activity = Activity.CreateReservoir,
                Index = 1
            });
            structureInfo.Sections[0].SectionReviewData.SectionReview.StructureType.Activities.Add(new()
            {
                Id = Guid.NewGuid(),
                Activity = Activity.TailingsBeach,
                Index = 2
            });

            return structureInfo;
        }

        public static StructureInfo CreateScenario11()
        {
            var currentDirectory = Directory.GetCurrentDirectory();

            var dxfPath = Path.Combine(currentDirectory, "StabilityAnalysis", "Files", "structure1.dxf");
            var sliPath = Path.Combine(currentDirectory, "StabilityAnalysis", "Files", "structure1.sli");

            var dxf = System.IO.File.ReadAllText(dxfPath);
            var sli = System.IO.File.ReadAllText(sliPath);

            var dxfBase64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(dxf));
            var sliBase64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(sli));

            var structureInfo = CreateDefault(dxfBase64, sliBase64);
            structureInfo.Sections[0].SectionReviewData.SectionReview.StructureType.Activities[0].Activity = Activity.TailingsBeach;
            structureInfo.Sections[0].UpstreamLinimetricRulerQuota = null;
            structureInfo.Sections[0].BeachLength = 800;

            return structureInfo;
        }

        public static StructureInfo CreateScenario12()
        {
            var currentDirectory = Directory.GetCurrentDirectory();

            var dxfPath = Path.Combine(currentDirectory, "StabilityAnalysis", "Files", "structure1.dxf");
            var sliPath = Path.Combine(currentDirectory, "StabilityAnalysis", "Files", "structure1.sli");

            var dxf = System.IO.File.ReadAllText(dxfPath);
            var sli = System.IO.File.ReadAllText(sliPath);

            var dxfBase64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(dxf));
            var sliBase64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(sli));

            var structureInfo = CreateDefault(dxfBase64, sliBase64);
            structureInfo.Sections[0].SectionReviewData.SectionReview.StructureType.Activities[0].Activity = Activity.TailingsBeach;
            structureInfo.Sections[0].UpstreamLinimetricRulerQuota = null;
            structureInfo.Sections[0].BeachLength = 50;

            return structureInfo;
        }

        public static StructureInfo CreateScenario13()
        {
            var currentDirectory = Directory.GetCurrentDirectory();

            var dxfPath = Path.Combine(currentDirectory, "StabilityAnalysis", "Files", "structure1.dxf");
            var dxfText = System.IO.File.ReadAllText(dxfPath);
            var dxfBase64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(dxfText));

            var dxf = new DxfFile();
            dxf = dxf.LoadWithBase64(dxfBase64);

            var layer = new DxfLayer("limites");
            dxf.Layers.Add(layer);

            var circle1 = new DxfCircle()
            {
                Center = new(170.6744, 874.4184, 0),
                Radius = 1,
                Layer = "limites"
            };

            dxf.Entities.Add(circle1);

            var circle2 = new DxfCircle()
            {
                Center = new(349.5701, 692.5631, 0),
                Radius = 1,
                Layer = "limites"
            };

            dxf.Entities.Add(circle2);

            var circle3 = new DxfCircle()
            {
                Center = new(452.5008, 896.8704, 0),
                Radius = 1,
                Layer = "limites"
            };

            dxf.Entities.Add(circle3);

            var circle4 = new DxfCircle()
            {
                Center = new(577.4647, 747.8102, 0),
                Radius = 1,
                Layer = "limites"
            };

            dxf.Entities.Add(circle4);

            using var ms = new MemoryStream();
            dxf.Save(ms);
            ms.Position = 0;
            dxfBase64 = Convert.ToBase64String(ms.ToArray());

            var sliPath = Path.Combine(currentDirectory, "StabilityAnalysis", "Files", "structure1.sli");
            var sli = System.IO.File.ReadAllText(sliPath);
            var sliBase64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(sli));

            var structureInfo = CreateDefault(dxfBase64, sliBase64);

            return structureInfo;
        }

        public static StructureInfo CreateScenario14()
        {
            var currentDirectory = Directory.GetCurrentDirectory();

            var dxfPath = Path.Combine(currentDirectory, "StabilityAnalysis", "Files", "structure1.dxf");
            var dxfText = System.IO.File.ReadAllText(dxfPath);
            var dxfBase64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(dxfText));

            var dxf = new DxfFile();
            dxf = dxf.LoadWithBase64(dxfBase64);

            var layer = new DxfLayer("limites");
            dxf.Layers.Add(layer);

            var circle1 = new DxfCircle()
            {
                Center = new(1, 74.4184, 0),
                Radius = 1,
                Layer = "limites"
            };

            dxf.Entities.Add(circle1);

            var circle2 = new DxfCircle()
            {
                Center = new(368.1274, 1692.5631, 0),
                Radius = 1,
                Layer = "limites"
            };

            dxf.Entities.Add(circle2);

            using var ms = new MemoryStream();
            dxf.Save(ms);
            ms.Position = 0;
            dxfBase64 = Convert.ToBase64String(ms.ToArray());

            var sliPath = Path.Combine(currentDirectory, "StabilityAnalysis", "Files", "structure1.sli");
            var sli = System.IO.File.ReadAllText(sliPath);
            var sliBase64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(sli));

            var structureInfo = CreateDefault(dxfBase64, sliBase64);

            return structureInfo;
        }
    }
}
