using IxMilia.Dxf;
using Slide.Core.Objects.Sli;
using TriangleNet.Geometry;
using Workflow.Extensions;
using Workflow.Tests.SharedBuilders;

namespace Workflow.Tests.Extensions.PolygonExtensions;

[Trait("PolygonExtensions ", "AddMaterialsWithContour")]
public class AddMaterialsWithContourTests
{
    [Theory(
        DisplayName =
            "When external layer exists, then should add the external")]
    [ClassData(typeof(DxfFileDataGenerator))]
    public void WhenExternalLayerExists_ShouldAddExternal(DxfFile dxfFile)
    {
        var materials = dxfFile.GetMaterialVertices();

        var polygon = new Polygon();

        polygon.AddMaterialsWithContour(materials);

        polygon.Points.Should().HaveCountGreaterThan(1);
    }

    [Fact(DisplayName = "When external vertices list is empty, then should not add any points ")]
    public void WhenListIsEmpty_ShouldNotAddAnyPoints()
    {
        var polygon = new Polygon();
        
        polygon.AddMaterialsWithContour(new List<(Vertice,int)>());
        
        polygon.Points.Should().BeEmpty();
    }
    
    [Fact(DisplayName = "When external vertices list is null, then should not add any points ")]
    public void WhenListIsNull_ShouldNotAddAnyPoints()
    {
        var polygon = new Polygon();
        
        polygon.AddMaterialsWithContour(null);
        
        polygon.Points.Should().BeEmpty();
    }
}
