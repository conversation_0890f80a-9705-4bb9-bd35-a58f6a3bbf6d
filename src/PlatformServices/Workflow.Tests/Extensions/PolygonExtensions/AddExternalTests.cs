using Dxf.Core.Extensions;
using IxMilia.Dxf;
using Slide.Core.Objects.Sli;
using TriangleNet.Geometry;
using Workflow.Extensions;
using Workflow.Tests.SharedBuilders;

namespace Workflow.Tests.Extensions.PolygonExtensions;

[Trait("PolygonExtensions", "AddExternal")]
public class AddExternalTests
{
    [Theory(
        DisplayName =
            "When external layer exists, then should add the external")]
    [ClassData(typeof(DxfFileDataGenerator))]
    public void WhenExternalLayerExists_ShouldAddExternal(DxfFile dxfFile)
    {
        var external = dxfFile
            .GetExternalPoints()
            .NormalizeVerticalEdges()
            .Select(point => (Vertice)point).ToList();

        var polygon = new Polygon();

        polygon.AddExternal(external);

        polygon.Points.Should().HaveCountGreaterThan(1);
    }

    [Fact(DisplayName = "When external vertices list is empty, then should not add any points ")]
    public void WhenListIsEmpty_ShouldNotAddAnyPoints()
    {
        var polygon = new Polygon();
        
        polygon.AddExternal(new List<Vertice>());
        
        polygon.Points.Should().BeEmpty();
    }
    
    [Fact(DisplayName = "When external vertices list is null, then should not add any points ")]
    public void WhenListIsNull_ShouldNotAddAnyPoints()
    {
        var polygon = new Polygon();
        
        polygon.AddExternal(null);
        
        polygon.Points.Should().BeEmpty();
    }
}
