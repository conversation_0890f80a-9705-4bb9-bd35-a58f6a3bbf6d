using System.Collections;

namespace Workflow.Tests.SharedBuilders;

public class DrawingDataGenerator : IEnumerable<object[]>
{
    public DrawingDataGenerator()
    {
        var currentDirectory = Directory.GetCurrentDirectory();
        var fileBasePath = Path.Combine(currentDirectory, "SharedBuilders", "RawDxfFiles");
        var fileNames = Directory.EnumerateFiles(fileBasePath).Where(file => file.Contains("17"));

        _data = fileNames
            .Select(name => new object[] { DrawingBuilder.CreateFile(name) })
            .ToList();
    }

    private readonly List<object[]> _data;

    public IEnumerator<object[]> GetEnumerator() => _data.GetEnumerator();

    IEnumerator IEnumerable.GetEnumerator() => GetEnumerator();
}
