using System.Text;
using Application.Apis.Clients;
using Microsoft.Extensions.Logging;
using Workflow.Tests.SharedBuilders;
using File = Application.Apis._Shared.File;

namespace Workflow.Tests.Section.Activities.CreateInitialSliFileActivity;

[Trait("CreateInitialSliFileActivity", "MountSliFile")]
public class MountSliFileTests
{
    private readonly Faker _faker = new();
    private readonly Mock<IClientsApiService> _clientsApiServiceMock = new();
    private readonly Mock<ILogger<Workflow.Section.CreateSliFile.Activities.CreateInitialSliFileActivity>> _loggerMock = new();
    
    [Fact(DisplayName = "When dxf file is null, should return null")]
    public void WhenDxfFileIsNull_ShouldReturnNull()
    {
        File dxfFile = null;
        var activity = new Workflow.Section.CreateSliFile.Activities.CreateInitialSliFileActivity(_clientsApiServiceMock.Object, _loggerMock.Object);

        var result = activity.MountSliFile(dxfFile);

        result.Should().BeNull();
    }

    [Fact(DisplayName = "When dxf file has null Base64, should return null")]
    public void WhenDxfFileHasNullBase64_ShouldReturnNull()
    {
        var dxfFile = new File
        {
            Base64 = null, Name = _faker.System.FileName("dxf")
        };
        var activity = new Workflow.Section.CreateSliFile.Activities.CreateInitialSliFileActivity(_clientsApiServiceMock.Object, _loggerMock.Object);

        var result = activity.MountSliFile(dxfFile);

        result.Should().BeNull();
    }

    [Fact(DisplayName = "When dxf file has empty Base64, should return null")]
    public void WhenDxfFileHasEmptyBase64_ShouldReturnNull()
    {
        var dxfFile = new File
        {
            Base64 = string.Empty, Name = _faker.System.FileName("dxf")
        };
        var activity = new Workflow.Section.CreateSliFile.Activities.CreateInitialSliFileActivity(_clientsApiServiceMock.Object, _loggerMock.Object);

        var result = activity.MountSliFile(dxfFile);

        result.Should().BeNull();
    }

    [Theory(DisplayName = "When dxf file has valid external and material vertices, should return valid SLI file")]
    [ClassData(typeof(DrawingDataGenerator))]
    public void WhenDxfFileHasValidExternalAndMaterialVertices_ShouldReturnValidSliFile(
        File dxfFile)
    {
        var activity = new Workflow.Section.CreateSliFile.Activities.CreateInitialSliFileActivity(_clientsApiServiceMock.Object, _loggerMock.Object);

        var result = activity.MountSliFile(dxfFile);

        result.Should().NotBeNull();
        result.Base64.Should().NotBeNullOrEmpty();
        result.Name.Should().EndWith(".sli");
    }
}
