using Application.Apis.Clients.Response.Section;
using Domain.Enums;

namespace Workflow.StabilityAnalysis.Classes
{
    public sealed class SectionInfo
    {
        public Guid SectionId { get; set; }
        public string SectionName { get; set; }
        public bool IsSkew { get; set; }
        public SectionCoordinates Coordinates { get; set; }
        public SectionReviewData SectionReviewData { get; set; }
        public List<InstrumentData> Instruments { get; set; }
        public double? BeachLength { get; set; }
        public decimal? UpstreamLinimetricRulerQuota { get; set; }
        public decimal? DownstreamLinimetricRulerQuota { get; set; }
        public double? MinimumDrainedDepth { get; set; }
        public double? MinimumUndrainedDepth { get; set; }
        public double? MinimumPseudoStaticDepth { get; set; }

        public bool ShouldUseBeachLength => BeachLength.HasValue 
            && SectionReviewData.SectionReview
                .StructureType.IsActivityPresent(Activity.TailingsBeach);
    }
}