using Application.Apis.Clients;
using Application.Apis.Clients.Response.Instrument;
using Application.Observability.Logs;
using Dxf.Core.Extensions;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Services.Models;
using Geometry.Core;
using Microsoft.Extensions.Logging;
using Workflow.Extensions;
using Workflow.StabilityAnalysis.Classes;
using Workflow.StabilityAnalysis.Patterns.Builder;
using static Workflow.Constants.StabilityAnalysisWorkFlow;
using Activity = Elsa.Services.Activity;
using Helper = Dxf.Core.Helper;

namespace Workflow.StabilityAnalysis._SharedActivities
{
    public sealed class MountSliFilesActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;
        private readonly ILogger<MountSliFilesActivity> _logger;
        private const double MaximumDistanceBetweenInstruments = 2.5;

        public MountSliFilesActivity(
            IClientsApiService clientsApiService,
            ILogger<MountSliFilesActivity> logger)
        {
            _clientsApiService = clientsApiService;
            _logger = logger;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                var structureInfo = context.GetTransientVariable<StructureInfo>(Variables.StructureInfo);

                var files = new List<SliDetailAggregator>();

                foreach (var sectionInfo in structureInfo.Sections)
                {
                    if (sectionInfo.SectionReviewData == null || sectionInfo.SectionReviewData.SectionReview == null)
                    {
                        _logger.LogSectionWithoutReviewsWarning(sectionInfo.SectionName);
                        context.SetTransientVariable(Variables.ErrorMessage, $"Não foi possível prosseguir com a análise de estabilidade da seção {sectionInfo.SectionName} pois essa seção não tem revisões.");
                        return Outcome(OutcomeNames.Cancel);
                    }

                    if (string.IsNullOrEmpty(sectionInfo.SectionReviewData.Sli?.Base64) || string.IsNullOrEmpty(sectionInfo.SectionReviewData.Dxf?.Base64))
                    {
                        _logger.LogSectionMissingFilesWarning(sectionInfo.SectionName);
                        context.SetTransientVariable(Variables.ErrorMessage, $"Não conseguimos continuar com a análise de estabilidade da seção {sectionInfo.SectionName} porque ela está sem os arquivos básicos necessários para iniciar o processo.");
                        return Outcome(OutcomeNames.Cancel);
                    }

                    var dxfFile = sectionInfo.SectionReviewData.Dxf.LoadDxfFile();

                    var fillingEntities = dxfFile.GetFillingEntities();

                    if (!fillingEntities.Any())
                    {
                        _logger.LogSectionWithoutFillingsWarning(sectionInfo.SectionName);
                        context.SetTransientVariable(Variables.ErrorMessage, $"O DXF da seção {sectionInfo.SectionName} não possui entidades de preenchimento (SOLID ou HATCH).");
                        return Outcome(OutcomeNames.Cancel);
                    }

                    var materialSearchIdentifiers = fillingEntities.GetMaterialSearchIdentifiers();

                    if (!materialSearchIdentifiers.Any())
                    {
                        _logger.LogDxfWithoutMaterialsWarning(sectionInfo.SectionName);
                        context.SetTransientVariable(Variables.ErrorMessage, $"O DXF da seção {sectionInfo.SectionName} não possui camadas com nome que represente o ID do material estático.");
                        return Outcome(OutcomeNames.Cancel);
                    }

                    var materials = await _clientsApiService
                       .GetStaticMaterialsBySearchIds(materialSearchIdentifiers, structureInfo.StaticMaterialReviewDate);

                    if (materials == null || !materials.Any())
                    {
                        _logger.LogSectionWithoutMaterialsWarning(sectionInfo.SectionName);
                        context.SetTransientVariable(Variables.ErrorMessage, $"Não foi possível prosseguir com a análise de estabilidade da seção {sectionInfo.SectionName} pois não foi possível encontrar os materiais estáticos.");
                        return Outcome(OutcomeNames.Cancel);
                    }

                    var externalPoints = dxfFile.GetExternalPoints().NormalizeVerticalEdges();

                    if (!externalPoints.Any())
                    {
                        _logger.LogDxfWithoutExternalPointsWarning(sectionInfo.SectionName);
                        context.SetTransientVariable(Variables.ErrorMessage, $"O DXF da seção {sectionInfo.SectionName} não possui pontos externos.");
                        return Outcome(OutcomeNames.Cancel);
                    }

                    var instrumentPoints = new InstrumentPoints();

                    if (sectionInfo.Instruments.Any() && !sectionInfo.SectionReviewData.SectionReview.DxfHasWaterline)
                    {
                        var instrumentInfos = await GetInstrumentInfos(sectionInfo);

                        instrumentPoints = dxfFile.GetInstrumentPoints(sectionInfo, instrumentInfos);

                        if (!instrumentPoints.Piezometers.Any() && !instrumentPoints.WaterLevelIndicators.Any())
                        {
                            _logger.LogSectionWithoutPercolationInstrumentsWarning(sectionInfo.SectionName);
                            context.SetTransientVariable(Variables.ErrorMessage, $"A seção {sectionInfo.SectionName} não possui INAs e PZs.");
                            return Outcome(OutcomeNames.Cancel);
                        }
                    }
                    else if (sectionInfo.SectionReviewData.SectionReview.DxfHasWaterline)
                    {
                        var waterLinePoints = dxfFile.GetInstrumentPoints(sectionInfo);

                        if (sectionInfo.Instruments.Any())
                        {
                            var instrumentInfos = await GetInstrumentInfos(sectionInfo);

                            var points2 = dxfFile.GetInstrumentPoints(sectionInfo, instrumentInfos);
                            
                            var instrumentPointsFromDxf =
                                points2.Piezometers.Concat(points2
                                    .WaterLevelIndicators);

                            foreach (var instrumentPoint in instrumentPointsFromDxf)
                            {
                                var allWaterLinePoints = waterLinePoints.Piezometers.Concat(waterLinePoints.WaterLevelIndicators).ToList();

                                var closestPoint = new Instrument();
                                var closestDistance = double.MaxValue;

                                foreach (var waterLinePoint in allWaterLinePoints)
                                {
                                    var distance = waterLinePoint.PointD.GetXAxisDistance(instrumentPoint.PointD);
                                    if (distance < closestDistance)
                                    {
                                        closestDistance = distance;
                                        closestPoint = waterLinePoint;
                                    }
                                }

                                if (closestDistance <= MaximumDistanceBetweenInstruments)
                                {
                                    closestPoint.PointD = new PointD(closestPoint.PointD.X, instrumentPoint.PointD.Y);
                                    closestPoint.InstrumentInfo = instrumentPoint.InstrumentInfo;
                                }
                                else if (closestPoint != null)  
                                {
                                    var elementIndex = waterLinePoints.WaterLevelIndicators.IndexOf(closestPoint);
                                    var index = closestPoint.PointD.X < instrumentPoint.PointD.X ? elementIndex + 1 : elementIndex;

                                    if (index == 0)
                                    {
                                        index++;
                                    }

                                    waterLinePoints.WaterLevelIndicators.Insert(index, instrumentPoint);
                                }
                                else
                                {
                                    waterLinePoints.WaterLevelIndicators.Add(instrumentPoint);
                                }
                            }
                        }

                        instrumentPoints = waterLinePoints;
                    }
                    else
                    {
                        _logger.LogDxfWithoutPercolationInstrumentsNorWaterLineWarning(sectionInfo.SectionName);
                        context.SetTransientVariable(Variables.ErrorMessage, $"A seção {sectionInfo.SectionName} não possui instrumentos e não possui linha freática no DXF.");
                        return Outcome(OutcomeNames.Cancel);
                    }

                    var fixedPoints = dxfFile.GetFixedPoints();

                    var beachLength = sectionInfo.BeachLength;

                    var circularParameters = structureInfo.Slide2Configuration?.CircularParameters;
                    var nonCircularParameters = structureInfo.Slide2Configuration?.NonCircularParameters;

                    if (circularParameters == null && nonCircularParameters == null)
                    {
                        _logger.LogStructureWithoutCircularAndNonCircularParametersWarning(structureInfo.StructureName);
                        context.SetTransientVariable(Variables.ErrorMessage, $"A estrutura {structureInfo.StructureName} não possui parâmetros circulares ou não circulares.");
                        return Outcome(OutcomeNames.Cancel);
                    }

                    if (circularParameters != null && !circularParameters.CalculationMethods.Any())
                    {
                        _logger.LogStructureWithoutCircularParametersWarning(structureInfo.StructureName);
                        context.SetTransientVariable(Variables.ErrorMessage, $"A estrutura {structureInfo.StructureName} não possui parâmetros circulares.");
                        return Outcome(OutcomeNames.Cancel);
                    }

                    if (nonCircularParameters != null && !nonCircularParameters.CalculationMethods.Any())
                    {
                        _logger.LogStructureWithoutNonCircularParametersWarning(structureInfo.StructureName);
                        context.SetTransientVariable(Variables.ErrorMessage, $"A estrutura {structureInfo.StructureName} não possui parâmetros não-circulares.");
                        return Outcome(OutcomeNames.Cancel);
                    }

                    var polygons = Helper.GetPolygons(fillingEntities);

                    if (!polygons.Any() || !polygons.Select(x => x.Layer).Any())
                    {
                        _logger.LogDxfWithoutPolygonsWarning(sectionInfo.SectionName);
                        context.SetTransientVariable(Variables.ErrorMessage, $"O DXF da seção {sectionInfo.SectionName} não possui polígonos, que definem as regiões dos materiais e que são gerados a partir de entidades do tipo HATCH ou SOLID.");
                        return Outcome(OutcomeNames.Cancel);
                    }

                    var useBeachLength = sectionInfo.ShouldUseBeachLength; 
                    var slopeLimits = dxfFile.GetSlopeLimits();

                    var sliFiles = new SliFileBuilder()
                        .Load(sectionInfo.SectionReviewData.Sli.Base64, structureInfo, materials)
                        .SetParameters(circularParameters, nonCircularParameters, structureInfo.SafetyFactorTarget, sectionInfo, dxfFile)
                        .SetMaterialProperties(materials)
                        .SetCellMaterials(materials, polygons)
                        .SetWaterLine(externalPoints, fixedPoints, instrumentPoints, sectionInfo.SectionReviewData.SectionReview, useBeachLength ? beachLength : null, sectionInfo.UpstreamLinimetricRulerQuota, sectionInfo.DownstreamLinimetricRulerQuota, sectionInfo.SectionReviewData.SectionReview.DxfHasWaterline)
                        .SetSeismicLoad(structureInfo)
                        .SetSlopeLimits(slopeLimits, externalPoints)
                        .Build();

                    foreach (var sliFile in sliFiles)
                    {
                        files.Add(new SliDetailAggregator
                        {
                            SliFile = sliFile.Item1,
                            SliFileType = sliFile.Item2,
                            DxfFile = dxfFile,
                            Section = sectionInfo,
                            Instruments = instrumentPoints,
                            Structure = structureInfo
                        });
                    }
                }

                context.SetTransientVariable(Variables.SliFiles, files);

                return Done();
            }
            catch (Exception e)
            {
                _logger.LogMountSliFilesUnexpectedError(e);
                context.JournalData.Add(Variables.ErrorMessage, $"Não conseguimos continuar com a análise de estabilidade porque houve um erro ao montar os arquivos necessários: {e.Message}");
                context.SetTransientVariable(Variables.ErrorMessage, $"Não conseguimos continuar com a análise de estabilidade porque houve um erro ao montar os arquivos necessários: {e.Message}");
                return Outcome(OutcomeNames.Cancel);
            }
        }

        private async Task<List<GetInstrumentByIdResponse>> GetInstrumentInfos(
            SectionInfo section)
        {
            var ids = section.Instruments
                .Select(instrument => instrument.InstrumentId)
                .Distinct();
            
            return (await _clientsApiService.GetInstrumentsByIds(ids))?
                .ToList();
        }
    }
}
