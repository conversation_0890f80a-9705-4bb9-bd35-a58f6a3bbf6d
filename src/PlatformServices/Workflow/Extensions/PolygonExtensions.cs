using Geometry.Core;
using NetTopologySuite.Geometries;
using Slide.Core.Objects.Sli;
using TriangleNet.Geometry;
using Polygon = TriangleNet.Geometry.Polygon;

namespace Workflow.Extensions;

public static class PolygonExtensions
{
    public static Polygon AddExternal(
        this Polygon polygon,
        List<Vertice> externalVertices)
    {
        if (externalVertices is null || !externalVertices.Any())
        {
            return polygon;
        }
        
        externalVertices = externalVertices.RemoveCloseVertices();
        
        var contour = new Contour(
            externalVertices.Select(x => new Vertex(x.X, x.Y, 1)),
            1);

        polygon.Add(contour);

        return polygon;
    }

    public static Polygon AddMaterialsWithSegments(
        this Polygon polygon,
        List<(Vertice Vertice, int MaterialId)> materials)
    {
        if (materials is null || !materials.Any())
        {
            return polygon;
        }
        
        var allValidMaterialVertices = materials
            .Select(x => x.Vertice)
            .ToList()
            .RemoveCloseVertices();
        
        var groupedMaterials = materials
            .Where(item => allValidMaterialVertices.Any(validVertex => (PointD)validVertex == (PointD)item.Vertice))
            .GroupBy(item => item.MaterialId);

        foreach (var group in groupedMaterials)
        {
            var vertices = group.Select(item => new Vertex(item.Vertice.X, item.Vertice.Y)).ToList();

            for (var i = 0; i < vertices.Count - 1; i++)
            {
                var existingVertex1 = polygon.Points.FirstOrDefault(vertex => vertex == vertices[i]);
                var existingVertex2 = polygon.Points.FirstOrDefault(vertex => vertex == vertices[i + 1]);
                var vertex1 = existingVertex1 ?? vertices[i];
                var vertex2 = existingVertex2 ?? vertices[i + 1];
                var segment = new Segment(vertex1, vertex2, group.Key + 1);
                
                if (polygon.Segments.Any(s => s.GetVertex(0) == segment.GetVertex(0) && s.GetVertex(1) == segment.GetVertex(1)))
                {
                    continue;
                }
                
                Action action = (existingVertex1, existingVertex2) switch
                {
                    (null, null) => () => polygon.Add(segment, true),
                    (null, _) => () => polygon.Add(segment, 0),
                    (_, null) =>  () => polygon.Add(segment, 1),
                    (_, _) => () =>  polygon.Add(segment)
                };

                action();
            }
        }

        return polygon;
    }
    
    public static Polygon AddMaterialsWithContour(
        this Polygon polygon,
        List<(Vertice Vertice, int MaterialId)> materials)
    {
        if (materials is null || !materials.Any())
        {
            return polygon;
        }
        
        var allValidMaterialVertices = materials
            .Select(x => x.Vertice)
            .ToList()
            .RemoveCloseVertices();
        
        var groupedMaterials = materials
            .Where(item => allValidMaterialVertices.Any(validVertex => (PointD)validVertex == (PointD)item.Vertice))
            .GroupBy(item => item.MaterialId);
        
        foreach (var group in groupedMaterials)
        {
            var vertices = group.Select(item => new Vertex(item.Vertice.X, item.Vertice.Y)).ToList();

            if (vertices.Count <= 1)
            {
                continue;
            }
            var contour = new Contour(vertices, group.Key);
            polygon.Add(contour);
        }

        return polygon;
    }
}