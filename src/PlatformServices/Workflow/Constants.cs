namespace Workflow
{
    public static class Constants
    {
        public const string ImagesPath = "images";
        public const string LogisoilLogoFileName = "logisoil-logo.png";
        public const string TempStructureImageFileName = "temp-structure-image.png";
        public const decimal Gravity = 9.81m;

        public static class AutomatedReadingsWorkFlow
        {
            public const string SftpProcessedFolderName = "{0}/processed";

            public static class Variables
            {
                public const string ClientConfigurations = "ClientConfigurations";
                public const string CurrentValue = "CurrentValue";
                public const string DownloadedFiles = "DownloadedFiles";
                public const string FilesConvertedToReadings = "FilesConvertedToReadings";
                public const string FilesLastFetchDate = "FilesLastFetchDate";
                public const string ReadingsToBeSent = "ReadingsToBeSent";
                public const string TemplateType = "TemplateType";
                public const string InstrumentIdentifiers = "InstrumentIdentifiers";
                public const string ErrorMessage = "ErrorMessage";

                public static class AtlanticNickel
                {
                    public const string LastRowNumberDatProcessed = "AtlanticNickelLastRowNumberDatProcessed";
                    public const string LastRowNumberCsvProcessed = "AtlanticNickelLastRowNumberCsvProcessed";
                    public const string LastInstrumentsRowNumberXlsxProcessed = "AtlanticNickelLastInstrumentsRowNumberXlsxProcessed";
                    public const string LastPluviometerRowNumberXlsxProcessed = "AtlanticNickelLastPluviometerRowNumberXlsxProcessed";
                    public const string SantaRitaStructureId = "AtlanticNickelSantaRitaStructureId";
                }
            }

            public static class FileExtensions
            {
                public const string Dat = ".dat";
                public const string Csv = ".csv";
                public const string Xlsx = ".xlsx";
            }
        }

        public static class ClientWorkFlow
        {
            public static class Variables
            {
                public const string CurrentValue = "CurrentValue";
                public const string ExpiringClients = "ExpiringClients";
                public const string UsersToNotify = "UsersToNotify";
            }
        }

        public static class NotificationWorkFlow
        {
            public const string UnknownUser = "Desconhecido";

            public static class Variables
            {
                public const string Message = "Message";
                public const string Notification = "notification";
            }
        }

        public static class PackageWorkFlow
        {
            public static class Variables
            {
                public const string AlertInfos = "AlertInfos";
                public const string Command = "Command";
                public const string Instruments = "Instruments";
                public const string Package = "Package";
                public const string Sections = "Sections";
                public const string Structure = "Structure";
            }
        }

        public static class ReadingWorkFlow
        {
            public static class Variables
            {
                public const string Output = "Output";
                public const string PackageIds = "PackageIds";
                public const string CreatedPackages = "CreatedPackages";
                public const string CalculatedPackages = "CalculatedPackages";
                public const string RelatedReadingValuesIds = "RelatedReadingValuesIds";
                public const string ReadingEvent = "ReadingEvent";
            }
        }

        public static class ReportWorkFlow
        {
            public static class Variables
            {
                public const string CronExpression = "CronExpression";
                public const string CurrentIndex = "CurrentIndex";
                public const string CurrentValue = "CurrentValue";
                public const string HoursToLookBack = "HoursToLookBack";
                public const string PendingReports = "PendingReports";
                public const string Report = "Report";
                public const string ReportFilePath = "Report_Path";
                public const string ReportFileUploaded = "Report_File_Uploaded";
                public const string ReportEmailSent = "Report_Email_Sent";
                public const string NoDataEmailSent = "No_Data_Email_Sent";
                public const string ReportUpdatedDates = "Report_Updated_Dates";
            }
        }

        public static class OccurrenceListReportWorkflow
        {
            public static class Variables
            {
                public const string Command = "Command";
                public const string OccurrenceListReport = "OccurrenceListReport";
                public const string OccurrenceListReportModel = "OccurrenceListReportModel";
            }
        }

        public static class SectionWorkFlow
        {
            public static class Variables
            {
                public const string Command = "Command";
            }
        }

        public static class SimulationWorkFlow
        {
            public static class Variables
            {
                public const string Command = "Command";
                public const string Simulation = "Simulation";
            }
        }

        public static class StabilityAnalysisWorkFlow
        {
            public static class Variables
            {
                public const string Command = "Command";
                public const string SliFiles = "SliFiles";
                public const string StructureInfo = "StructureInfo";
                public const string ErrorMessage = "ErrorMessage";
                public const string StructureId = "StructureId";
            }
        }

        public static class InspectionWorkflow
        {
            public static class Variables
            {
                public const string Command = "Command";
                public const string InspectionSheet = "InspectionSheet";
            }
        }
    }
}
